import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { WorksheetService } from './worksheet.service';
import { WorksheetGeneratingStatus } from './entities/worksheet.entity';
import { DocumentsService } from '../documents/documents.service';
import { PromptService } from '../prompt/services/prompt.service';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { WorksheetPromptResult } from '../mongodb/schemas/worksheet-prompt-result.schema';
import { SocketGateway } from '../socket/socket.gateway';
import { BatchImageService } from '../gen-image/batch-image.service';
import { WorksheetDocumentCacheService } from './services/worksheet-document-cache.service';
import { QuestionPoolService } from '../question-pool/question-pool.service';
import { QuestionPoolConfigService } from '../question-pool/question-pool-config.service';
import { ContentValidationService } from '../validation/content-validation.service';
import { SelectionStrategy } from './dto/worksheet-generation-options.dto';
import { ExerciseQuestionItem, ExerciseResult } from '../prompt/interfaces/exercise-result.interface';
import { QuestionPool } from '../mongodb/schemas/question-pool.schema';
import { AiOrchestrationService } from '../ai/ai-orchestration.service';
import { WebSocketErrorCode } from '../../core/enums/websocket-error-codes.enum';
import { PoolMonitoringService } from '../monitoring/services/pool-monitoring.service';
import { WorksheetGenerationEvent } from '../monitoring/interfaces/monitoring-events.interface';
import { v4 as uuidv4 } from 'uuid';

@Processor('worksheet_generate')
@Injectable()
export class WorksheetGenerateConsumer extends WorkerHost {
  private readonly logger = new Logger(WorksheetGenerateConsumer.name);
  constructor(
      private worksheetService: WorksheetService,
      private documentsService: DocumentsService,
      private promptService: PromptService,
      @InjectModel(WorksheetPromptResult.name)
      private worksheetPromptResultModel: Model<WorksheetPromptResult>,
      private socketGateway: SocketGateway,
      private batchImageService: BatchImageService,
      private worksheetDocumentCacheService: WorksheetDocumentCacheService,
      private questionPoolService: QuestionPoolService,
      private questionPoolConfigService: QuestionPoolConfigService,
      private contentValidationService: ContentValidationService,
      private aiOrchestrationService: AiOrchestrationService,
      private poolMonitoringService: PoolMonitoringService,
  ) {
    super();
  }

  /**
   * Maps frontend question type labels to backend format
   * @param label The frontend label (e.g., "Fill Blank", "Creative Writing")
   * @returns The backend format (e.g., "fill_blank", "creative_writing")
   */
  private mapQuestionTypeLabel(label: string): string {
    const mappings: Record<string, string> = {
      'Fill Blank': 'fill_blank',
      'Creative Writing': 'creative_writing',
      'Multiple Choices': 'multiple_choice',
      'Single Choice': 'single_choice',
      'Multiple Choice': 'multiple_choice',
      'Open Ended': 'open_ended',
      'Matching': 'matching'
    };

    return mappings[label] || label.toLowerCase().replace(/\s+/g, '_');
  }

  /**
   * Main orchestration method for hybrid question sourcing
   * Implements the strategy pattern based on resolved configuration
   */
  private async processHybridQuestionSourcing(params: {
    worksheetId: string;
    resolvedOptions: any;
    documentResults: any;
    userRequest: any;
  }): Promise<ExerciseResult> {
    const { worksheetId, resolvedOptions, documentResults, userRequest } = params;
    const strategy = resolvedOptions.resolvedStrategy;
    const totalQuestions = userRequest.totalQuestions;

    this.logger.log(`Starting hybrid question sourcing with strategy: ${strategy} for ${totalQuestions} questions`);

    let poolQuestions: ExerciseQuestionItem[] = [];
    let aiQuestions: ExerciseQuestionItem[] = [];
    let allQuestions: ExerciseQuestionItem[] = [];

    // Emit initial progress for hybrid sourcing
    this.socketGateway.emitWorksheetProgress(worksheetId, 0, totalQuestions, 'Starting question sourcing...');

    try {
      switch (strategy) {
        case SelectionStrategy.POOL_ONLY:
          this.logger.log('Using POOL_ONLY strategy');
          this.socketGateway.emitWorksheetProgress(worksheetId, 0, totalQuestions, 'Fetching questions from pool...');
          poolQuestions = await this.getQuestionsFromPool(userRequest, totalQuestions, worksheetId);
          allQuestions = poolQuestions;

          if (poolQuestions.length < totalQuestions) {
            const deficit = totalQuestions - poolQuestions.length;
            this.logger.warn(`POOL_ONLY strategy: Only ${poolQuestions.length}/${totalQuestions} questions available in pool`);
            this.socketGateway.emitQuestionPoolError(
              worksheetId,
              WebSocketErrorCode.INSUFFICIENT_QUESTIONS_NO_FALLBACK,
              `Only ${poolQuestions.length} of ${totalQuestions} requested questions available in pool. No fallback allowed with POOL_ONLY strategy.`,
              { available: poolQuestions.length, requested: totalQuestions, deficit }
            );
          }

          this.socketGateway.emitWorksheetProgress(worksheetId, poolQuestions.length, totalQuestions, 'Pool questions retrieved');
          break;

        case SelectionStrategy.AI_ONLY:
          this.logger.log('Using AI_ONLY strategy');
          this.socketGateway.emitWorksheetProgress(worksheetId, 0, totalQuestions, 'Generating questions with AI...');
          aiQuestions = await this.generateQuestionsWithAI(documentResults, userRequest, worksheetId);
          allQuestions = aiQuestions;
          break;

        case SelectionStrategy.HYBRID:
          this.logger.log('Using HYBRID strategy (pool first, AI fallback)');
          // Try to get questions from pool first
          this.socketGateway.emitWorksheetProgress(worksheetId, 0, totalQuestions, 'Fetching questions from pool...');
          poolQuestions = await this.getQuestionsFromPool(userRequest, totalQuestions, worksheetId);

          const deficit = totalQuestions - poolQuestions.length;
          if (deficit > 0) {
            this.logger.log(`Pool provided ${poolQuestions.length}/${totalQuestions} questions. Generating ${deficit} questions with AI.`);

            // Emit fallback activation notification
            this.socketGateway.emitFallbackActivated(
              worksheetId,
              'question_pool_selection',
              'ai_generation',
              `Insufficient questions in pool: ${poolQuestions.length}/${totalQuestions} available`
            );

            this.socketGateway.emitWorksheetProgress(worksheetId, poolQuestions.length, totalQuestions, `Pool: ${poolQuestions.length}, generating ${deficit} with AI...`);

            // Generate remaining questions with AI
            const aiUserRequest = { ...userRequest, totalQuestions: deficit };
            aiQuestions = await this.generateQuestionsWithAI(documentResults, aiUserRequest, worksheetId);

            if (aiQuestions.length === 0) {
              this.logger.error(`AI fallback failed to generate any questions for deficit of ${deficit}`);
              this.socketGateway.emitSystemError(
                worksheetId,
                WebSocketErrorCode.FALLBACK_CHAIN_EXHAUSTED,
                `Both question pool and AI generation failed. Pool: ${poolQuestions.length}/${totalQuestions}, AI: 0/${deficit}`,
                { poolQuestions: poolQuestions.length, aiQuestions: 0, totalRequested: totalQuestions }
              );
            }
          } else {
            this.logger.log(`Pool provided sufficient questions: ${poolQuestions.length}/${totalQuestions}`);
            this.socketGateway.emitWorksheetProgress(worksheetId, poolQuestions.length, totalQuestions, 'Pool provided all questions');
          }

          allQuestions = [...poolQuestions, ...aiQuestions];
          break;

        case SelectionStrategy.MIXED:
          this.logger.log('Using MIXED strategy (combination of pool and AI)');
          const poolTarget = Math.floor(totalQuestions * 0.5); // 50% from pool
          const aiTarget = totalQuestions - poolTarget;

          this.logger.log(`MIXED strategy: targeting ${poolTarget} from pool, ${aiTarget} from AI`);
          this.socketGateway.emitWorksheetProgress(worksheetId, 0, totalQuestions, `Mixed: ${poolTarget} from pool, ${aiTarget} from AI...`);

          poolQuestions = await this.getQuestionsFromPool(userRequest, poolTarget, worksheetId);
          this.socketGateway.emitWorksheetProgress(worksheetId, poolQuestions.length, totalQuestions, `Pool: ${poolQuestions.length}, generating AI questions...`);

          const aiUserRequest = { ...userRequest, totalQuestions: aiTarget };
          aiQuestions = await this.generateQuestionsWithAI(documentResults, aiUserRequest, worksheetId);

          // Check if we got the expected distribution
          if (poolQuestions.length < poolTarget || aiQuestions.length < aiTarget) {
            this.logger.warn(
              `MIXED strategy did not achieve target distribution. ` +
              `Pool: ${poolQuestions.length}/${poolTarget}, AI: ${aiQuestions.length}/${aiTarget}`
            );
          }

          allQuestions = [...poolQuestions, ...aiQuestions];
          break;

        default:
          this.logger.warn(`Unknown strategy: ${strategy}. Falling back to AI_ONLY`);
          this.socketGateway.emitWorksheetProgress(worksheetId, 0, totalQuestions, 'Fallback: generating with AI...');
          aiQuestions = await this.generateQuestionsWithAI(documentResults, userRequest, worksheetId);
          allQuestions = aiQuestions;
      }

      // Validate all questions
      this.socketGateway.emitWorksheetProgress(worksheetId, allQuestions.length, totalQuestions, 'Validating questions...');
      const validatedQuestions = await this.validateQuestions(allQuestions);

      // Ensure we have the exact number of questions requested
      this.socketGateway.emitWorksheetProgress(worksheetId, validatedQuestions.length, totalQuestions, 'Finalizing question count...');
      const finalQuestions = await this.ensureQuestionCount(validatedQuestions, totalQuestions);

      this.logger.log(`Hybrid sourcing completed: ${poolQuestions.length} from pool, ${aiQuestions.length} from AI, ${finalQuestions.length} final`);
      this.socketGateway.emitWorksheetProgress(worksheetId, finalQuestions.length, totalQuestions, 'Question sourcing completed');

      // Emit monitoring event for analytics
      await this.emitWorksheetGenerationEvent(
        worksheetId,
        strategy,
        totalQuestions,
        poolQuestions.length,
        aiQuestions.length,
        finalQuestions.length,
        validatedQuestions,
        userRequest
      );

      return { result: finalQuestions };

    } catch (error) {
      this.logger.error(`Error in hybrid question sourcing: ${error.message}`, error.stack);
      this.socketGateway.emitWorksheetProgress(worksheetId, 0, totalQuestions, `Error: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get questions from the question pool with comprehensive error handling
   */
  private async getQuestionsFromPool(userRequest: any, count: number, worksheetId?: string): Promise<ExerciseQuestionItem[]> {
    const startTime = Date.now();
    let retryAttempt = 0;
    const maxRetries = 3;

    try {
      this.logger.log(`Attempting to get ${count} questions from pool with filters: subject=${userRequest.subject}, grade=${userRequest.grade}, topic=${userRequest.topic}`);

      // Handle multiple subjects from the payload
      // The userRequest.subjectData contains all the subjects (Speed, Percentage, Decimals)
      let allQuestions: any[] = [];

      if (userRequest.subjectData && userRequest.subjectData.length > 0) {
        this.logger.log(`Found ${userRequest.subjectData.length} subjects in subjectData: ${userRequest.subjectData.map(s => s.label).join(', ')}`);

        // Calculate questions per subject
        const questionsPerSubject = Math.ceil(count / userRequest.subjectData.length);

        // Get questions for each subject
        for (const subjectItem of userRequest.subjectData) {
          const filters = {
            subject: userRequest.topic, // topic is the main subject (Mathematics)
            childSubject: subjectItem.label, // Each subject label (Speed, Percentage, Decimals)
            grade: userRequest.grade,
            language: 'English',
            status: 'active',
          };

          this.logger.log(`Querying for ${questionsPerSubject} questions with filters: subject=${filters.subject}, childSubject=${filters.childSubject}, grade=${filters.grade}`);

          try {
            const subjectQuestions = await this.questionPoolService.getRandomQuestions(filters, questionsPerSubject);
            allQuestions.push(...subjectQuestions);
            this.logger.log(`Found ${subjectQuestions.length} questions for ${subjectItem.label}`);
          } catch (error) {
            this.logger.warn(`Error getting questions for ${subjectItem.label}: ${error.message}`);
          }
        }

        // Shuffle and limit to requested count
        allQuestions = allQuestions.sort(() => Math.random() - 0.5).slice(0, count);

      } else {
        // Fallback to original logic if no subjectData
        this.logger.warn('No subjectData found, using fallback single subject query');
        const filters = {
          subject: userRequest.topic, // topic is the main subject (Mathematics)
          childSubject: userRequest.subject, // subject is the specific subtopic
          grade: userRequest.grade,
          language: 'English',
          status: 'active',
        };

        this.logger.log(`Fallback query with filters: subject=${filters.subject}, childSubject=${filters.childSubject}, grade=${filters.grade}`);
        allQuestions = await this.questionPoolService.getRandomQuestions(filters, count);
      }

      while (retryAttempt <= maxRetries) {
        try {
          // Emit retry attempt if this is not the first attempt
          if (retryAttempt > 0 && worksheetId) {
            this.socketGateway.emitRetryAttempt(
              worksheetId,
              'question_pool_query',
              retryAttempt,
              maxRetries,
              1000 * Math.pow(2, retryAttempt - 1) // Exponential backoff
            );
          }

          this.logger.log(`Retrieved ${allQuestions.length} questions from pool on attempt ${retryAttempt + 1}`);

          if (allQuestions.length === 0) {
            this.logger.warn(`No questions found in pool for any of the requested subjects`);

            if (worksheetId) {
              this.socketGateway.emitQuestionPoolError(
                worksheetId,
                WebSocketErrorCode.QUESTION_POOL_EMPTY,
                'No questions found in pool matching the specified criteria',
                { requestedSubjects: userRequest.subjectData?.map(s => s.label) || [userRequest.subject], requestedCount: count }
              );
            }
          }

          // Convert QuestionPool to ExerciseQuestionItem format
          const convertedQuestions = allQuestions.map(this.convertPoolQuestionToExerciseItem);

          const duration = Date.now() - startTime;
          this.logger.log(`Question pool query completed successfully in ${duration}ms after ${retryAttempt + 1} attempts`);

          return convertedQuestions;

        } catch (attemptError) {
          retryAttempt++;

          // Check if this is a retryable error
          const isRetryable = this.isRetryableError(attemptError);

          if (!isRetryable || retryAttempt > maxRetries) {
            // Non-retryable error or max retries exceeded
            throw attemptError;
          }

          const delay = 1000 * Math.pow(2, retryAttempt - 1); // Exponential backoff
          this.logger.warn(
            `Question pool query attempt ${retryAttempt} failed: ${attemptError.message}. ` +
            `Retrying in ${delay}ms (${maxRetries - retryAttempt} attempts remaining)`
          );

          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }

      // This should not be reached due to the throw in the catch block above
      throw new Error('Unexpected error: retry loop completed without success or failure');

    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(
        `Error getting questions from pool after ${retryAttempt} attempts in ${duration}ms: ${error.message}`,
        error.stack
      );

      // Emit appropriate error based on error type
      if (worksheetId) {
        if (error.message.includes('timeout')) {
          this.socketGateway.emitDatabaseError(
            worksheetId,
            WebSocketErrorCode.DATABASE_TIMEOUT,
            'Question pool query timed out',
            'getRandomQuestions'
          );
        } else if (error.message.includes('connection') || error.message.includes('network')) {
          this.socketGateway.emitDatabaseError(
            worksheetId,
            WebSocketErrorCode.DATABASE_CONNECTION_FAILED,
            'Failed to connect to question pool database',
            'getRandomQuestions'
          );
        } else if (retryAttempt > maxRetries) {
          this.socketGateway.emitSystemError(
            worksheetId,
            WebSocketErrorCode.MAX_RETRIES_REACHED,
            `Question pool query failed after ${maxRetries} retry attempts`,
            { error: error.message, attempts: retryAttempt }
          );
        } else {
          this.socketGateway.emitQuestionPoolError(
            worksheetId,
            WebSocketErrorCode.QUESTION_POOL_UNAVAILABLE,
            'Question pool is temporarily unavailable',
            { error: error.message, attempts: retryAttempt }
          );
        }
      }

      // Return empty array on error to allow fallback strategies
      return [];
    }
  }

  /**
   * Check if an error is retryable
   */
  private isRetryableError(error: any): boolean {
    const retryableErrors = [
      'MongoNetworkError',
      'MongoTimeoutError',
      'MongoServerSelectionError',
      'ENOTFOUND',
      'ECONNREFUSED',
      'ETIMEDOUT',
      'ECONNRESET'
    ];

    const errorName = error.constructor.name;
    const errorCode = error.code;
    const errorMessage = error.message?.toLowerCase() || '';

    // Check for retryable error types
    if (retryableErrors.includes(errorName)) {
      return true;
    }

    // Check for specific error codes
    if (errorCode && ['ENOTFOUND', 'ECONNREFUSED', 'ETIMEDOUT', 'ECONNRESET'].includes(errorCode)) {
      return true;
    }

    // Check for connection-related error messages
    if (errorMessage.includes('connection') ||
        errorMessage.includes('timeout') ||
        errorMessage.includes('network') ||
        errorMessage.includes('server selection')) {
      return true;
    }

    return false;
  }

  /**
   * Generate questions using AI with comprehensive error handling and fallback chain
   */
  private async generateQuestionsWithAI(documentResults: any, userRequest: any, worksheetId: string): Promise<ExerciseQuestionItem[]> {
    const startTime = Date.now();

    try {
      this.logger.log(`Generating ${userRequest.totalQuestions} questions with AI orchestration service`);

      // Prepare the AI generation request
      const aiRequest = {
        userRequest,
        documentResults,
        worksheetId,
        questionCount: userRequest.totalQuestions,
        subject: userRequest.subject,
        grade: userRequest.grade,
        questionTypes: userRequest.exerciseTypes,
      };

      // Use the AI orchestration service for resilient generation
      const orchestrationResult = await this.aiOrchestrationService.generateQuestions(aiRequest);

      // Log the orchestration result details
      this.logger.log(
        `AI orchestration completed: provider=${orchestrationResult.provider}, ` +
        `success=${orchestrationResult.success}, questions=${orchestrationResult.questions.length}, ` +
        `duration=${orchestrationResult.totalDuration}ms`
      );

      // Emit progress updates based on the result
      if (orchestrationResult.success && orchestrationResult.questions.length > 0) {
        this.socketGateway.emitWorksheetProgress(
          worksheetId,
          orchestrationResult.questions.length,
          userRequest.totalQuestions,
          `AI generated ${orchestrationResult.questions.length} questions using ${orchestrationResult.provider}`
        );

        return orchestrationResult.questions;
      } else {
        // All AI providers failed
        this.logger.error(
          `All AI providers failed for worksheet ${worksheetId}. ` +
          `OpenAI: ${orchestrationResult.attempts.openai?.error || 'not attempted'}, ` +
          `Google AI: ${orchestrationResult.attempts.googleAi?.error || 'not attempted'}, ` +
          `Cached Content: ${orchestrationResult.attempts.cachedContent?.error || 'not attempted'}`
        );

        // Emit specific error events for each failed attempt
        if (orchestrationResult.attempts.openai && !orchestrationResult.attempts.openai.success) {
          this.socketGateway.emitAiServiceError(
            worksheetId,
            WebSocketErrorCode.OPENAI_SERVICE_FAILED,
            'OpenAI service failed to generate questions',
            'OpenAI via OpenRouter',
            orchestrationResult.attempts.openai
          );
        }

        if (orchestrationResult.attempts.googleAi && !orchestrationResult.attempts.googleAi.success) {
          this.socketGateway.emitAiServiceError(
            worksheetId,
            WebSocketErrorCode.GOOGLE_AI_SERVICE_FAILED,
            'Google AI service failed to generate questions',
            'Google AI',
            orchestrationResult.attempts.googleAi
          );
        }

        if (orchestrationResult.attempts.cachedContent && !orchestrationResult.attempts.cachedContent.success) {
          this.socketGateway.emitAiServiceError(
            worksheetId,
            WebSocketErrorCode.CACHED_CONTENT_UNAVAILABLE,
            'Cached content fallback failed',
            'Cached Content',
            orchestrationResult.attempts.cachedContent
          );
        }

        // Emit final error indicating all services failed
        this.socketGateway.emitAiServiceError(
          worksheetId,
          WebSocketErrorCode.ALL_AI_SERVICES_FAILED,
          'All AI services and fallbacks failed to generate questions',
          'All Providers',
          {
            totalDuration: orchestrationResult.totalDuration,
            attempts: orchestrationResult.attempts,
          }
        );

        return [];
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`Critical error in AI question generation after ${duration}ms: ${error.message}`, error.stack);

      // Emit system error for unexpected failures
      this.socketGateway.emitSystemError(
        worksheetId,
        WebSocketErrorCode.AI_SERVICE_FAILED,
        'Unexpected error in AI question generation system',
        {
          error: error.message,
          duration,
          stack: error.stack?.substring(0, 500), // Truncate stack trace
        }
      );

      return []; // Return empty array on error to allow graceful degradation
    }
  }

  /**
   * Convert QuestionPool to ExerciseQuestionItem format
   */
  private convertPoolQuestionToExerciseItem = (poolQuestion: QuestionPool): ExerciseQuestionItem => {
    return {
      type: poolQuestion.type,
      content: poolQuestion.content,
      options: poolQuestion.options,
      answer: poolQuestion.answer,
      explain: poolQuestion.explain,
      imagePrompt: poolQuestion.imagePrompt,
      image: poolQuestion.image,
      subject: poolQuestion.subject,
      parentSubject: poolQuestion.parentSubject,
      childSubject: poolQuestion.childSubject,
    };
  }

  /**
   * Validate all questions using ContentValidationService
   */
  private async validateQuestions(questions: ExerciseQuestionItem[]): Promise<ExerciseQuestionItem[]> {
    try {
      this.logger.log(`Validating ${questions.length} questions`);

      if (questions.length === 0) {
        this.logger.warn('No questions to validate');
        return [];
      }

      const validatedQuestions: ExerciseQuestionItem[] = [];
      let validationErrors = 0;

      for (let i = 0; i < questions.length; i++) {
        const question = questions[i];
        try {
          const validationResult = await this.contentValidationService.validateQuestion({
            id: `temp-${Date.now()}-${Math.random()}`,
            type: question.type,
            content: question.content,
            options: question.options,
            answer: question.answer,
            explain: question.explain,
            subject: question.subject,
          });

          if (validationResult.isValid) {
            validatedQuestions.push(question);
          } else {
            this.logger.warn(`Question ${i + 1} validation failed: ${validationResult.issues.map((i: any) => i.message).join(', ')}`);
            // For now, include failed questions to maintain count - could be made configurable
            validatedQuestions.push(question);
          }
        } catch (validationError) {
          validationErrors++;
          this.logger.error(`Error validating question ${i + 1}: ${validationError.message}`);
          // Include the question anyway if validation service fails
          validatedQuestions.push(question);
        }
      }

      const successRate = ((validatedQuestions.length - validationErrors) / questions.length) * 100;
      this.logger.log(`Validation completed: ${validatedQuestions.length}/${questions.length} questions processed, ${validationErrors} validation errors, ${successRate.toFixed(1)}% success rate`);

      return validatedQuestions;

    } catch (error) {
      this.logger.error(`Critical error in question validation: ${error.message}`, error.stack);
      // Return original questions if validation system fails completely
      return questions;
    }
  }

  /**
   * Ensure we have exactly the requested number of questions
   */
  private async ensureQuestionCount(questions: ExerciseQuestionItem[], targetCount: number): Promise<ExerciseQuestionItem[]> {
    if (questions.length === targetCount) {
      return questions;
    }

    if (questions.length > targetCount) {
      this.logger.log(`Trimming ${questions.length - targetCount} excess questions`);
      return questions.slice(0, targetCount);
    }

    // If we have fewer questions than needed, duplicate some
    if (questions.length > 0) {
      this.logger.log(`Need ${targetCount - questions.length} more questions. Duplicating existing ones.`);
      const result = [...questions];

      while (result.length < targetCount) {
        const randomIndex = Math.floor(Math.random() * questions.length);
        const questionToDuplicate = questions[randomIndex];

        // Create a slightly modified copy
        const modifiedQuestion = {
          ...questionToDuplicate,
          content: questionToDuplicate.content + ' ', // Add space to make it different
        };

        result.push(modifiedQuestion);
      }

      return result;
    }

    // If no questions available, return empty array
    this.logger.warn(`No questions available to meet target count of ${targetCount}`);
    return [];
  }

  async process(job: Job<any, any, string>): Promise<any> {
    const { worksheetId, topic, grade, questionCount, isCustomQuestionCount, questionSourceStrategy } = job.data;

    this.logger.log(`Processing worksheet generation job for worksheet ${worksheetId} with topic ${topic}, grade ${grade}, questionCount ${questionCount}, isCustom ${isCustomQuestionCount}, strategy ${questionSourceStrategy || 'default'}`);

    try {
      await this.worksheetService.updateStatus(
          worksheetId,
          WorksheetGeneratingStatus.PENDING,
      );

      // Use the WorksheetDocumentCacheService to look for cached documents
      this.logger.debug(`Looking for cached document results for topic: ${topic}, grade: ${grade}`);

      // Get worksheet to extract additional parameters for cache key
      const worksheet = await this.worksheetService.findOne(worksheetId);
      if (!worksheet) {
        throw new Error(`Worksheet with ID ${worksheetId} not found`);
      }

      let category = '';

      // Extract subject from worksheet data if available
      if (worksheet.subjectData && worksheet.subjectData.length > 0) {
        category = worksheet.subjectData[0].label || '';
      }

      // Additional parameters for more specific cache matching
      const additionalParams = { category };

      // Try to get from cache
      const existingDocument = await this.worksheetDocumentCacheService.getFromCache(
          topic,
          grade,
          additionalParams
      );

      let documentResults;
      if (existingDocument) {
        this.logger.debug(`Using cached document results from cache service (id: ${existingDocument._id})`);
        documentResults = existingDocument.documentResult;
      } else {
        this.logger.debug(`No cached document found, querying Pinecone DB`);
        const query = `Help me query subject teaching in ${topic} SYLLABUS ${grade}`;

        // Query documents with skipCache=false to use the QueryCacheService
        documentResults = await this.documentsService.queryDocuments(
            query,
            5,
            60000,
            category,
            false // Explicitly set skipCache to false
        );

        this.logger.debug(`Retrieved fresh document results from Pinecone DB`);
      }

      // Save the document results to cache using the WorksheetDocumentCacheService
      await this.worksheetDocumentCacheService.saveToCache(
          topic,
          grade,
          documentResults,
          worksheetId,
          !!existingDocument, // fromCache flag
          additionalParams
      );

      this.logger.debug(`Saved document results to cache service`);

      // Get the worksheet for further processing
      const worksheetDetails = await this.worksheetService.findOne(worksheetId);
      if (!worksheetDetails) {
        throw new Error(`Worksheet details with ID ${worksheetId} not found`);
      }

      const totalQuestions = questionCount;
      

      // Initialize the worksheet prompt result with 0 current questions and the total
      try {
        // Check if a record already exists
        const existingPromptResult = await this.worksheetPromptResultModel.findOne({
          worksheetId,
        });

        if (!existingPromptResult) {
          // Create a new record if none exists
          const initialPromptResult = new this.worksheetPromptResultModel({
            worksheetId,
            promptResult: { result: [] },
            currentQuestionCount: 0,
            totalQuestionCount: totalQuestions,
          });
          await initialPromptResult.save();

          // Emit initial progress
          this.socketGateway.emitWorksheetProgress(worksheetId, 0, totalQuestions);
        }
      } catch (saveError) {
        this.logger.error(`Failed to initialize prompt result: ${saveError.message}`, saveError.stack);
      }

      let promptResult: any = null;
      try {
        // Get subject and parentSubject from worksheetDetails.subjectData if available
        let subject = '';
        let parentSubject = '';

        if (worksheetDetails.subjectData && worksheetDetails.subjectData.length > 0) {
          // Use the first subject's label as the subject
          subject = worksheetDetails.subjectData[0].label || '';

          // Use the first item of the first subject as the parent subject if available
          if (worksheetDetails.subjectData[0].items && worksheetDetails.subjectData[0].items.length > 0) {
            parentSubject = worksheetDetails.subjectData[0].items[0] || '';
          }
        }

        // Get level/difficulty option
        const levelOption = worksheetDetails.selectedOptions.find(
            (opt) => opt.optionType.label === 'level'
        );
        const difficulty = levelOption?.optionValue.value || 'Medium';

        // Get question types and their distribution if available
        const questionTypeOptions = worksheetDetails.selectedOptions
            .filter((opt) => opt.optionType.key === 'question_type');

        // Build exercise type distribution and conditionally filter exercise types
        const exerciseTypeDistribution: Record<string, number> = {};
        let hasDistribution = false;
        let exerciseTypes: string[];

        if (isCustomQuestionCount) {
          this.logger.log('Custom question count is true. Filtering exercise types based on count > 0.');
          exerciseTypes = []; // Initialize as empty for custom count
          for (const option of questionTypeOptions) {
            if (option.count !== undefined && option.count > 0) {
              const mappedLabel = this.mapQuestionTypeLabel(option.optionValue.label);
              exerciseTypeDistribution[mappedLabel] = option.count;
              exerciseTypes.push(mappedLabel); // Add to exerciseTypes only if count > 0
              hasDistribution = true;
            }
          }
        } else {
          this.logger.log('Custom question count is false. Including all selected exercise types.');
          // Original behavior for non-custom count
          exerciseTypes = questionTypeOptions.map((opt) => this.mapQuestionTypeLabel(opt.optionValue.label));
          // Build distribution for all types that have a count > 0
          for (const option of questionTypeOptions) {
            if (option.count !== undefined && option.count > 0) {
              const mappedLabel = this.mapQuestionTypeLabel(option.optionValue.label);
              exerciseTypeDistribution[mappedLabel] = option.count;
              hasDistribution = true; 
            }
          }
        }
        
        // If no exercise types were derived (e.g., custom count with all zeros, or no options provided), use default types
        const finalExerciseTypes = exerciseTypes.length > 0
            ? exerciseTypes
            : ['multiple_choice', 'single_choice', 'creative_writing', 'fill_blank'];

        this.logger.log(`Final exercise types for prompt: ${JSON.stringify(finalExerciseTypes)}`);
        if (hasDistribution) {
          this.logger.log(`Exercise type distribution for prompt: ${JSON.stringify(exerciseTypeDistribution)}`);
        } else {
          this.logger.log('No exercise type distribution specified for prompt.');
        }
        
        // Extract schoolId from worksheet details for narrative structure retrieval
        const schoolId = worksheetDetails.schoolId;
        if (schoolId) {
          this.logger.log(`Using schoolId ${schoolId} from worksheet for narrative structure retrieval`);
        } else {
          this.logger.warn(`No schoolId found in worksheet ${worksheetId}, narrative structure retrieval may be limited`);
        }

        // Resolve question source strategy configuration
        const resolvedOptions = this.questionPoolConfigService.resolveWorksheetGenerationOptions({
          selectionStrategy: questionSourceStrategy,
        });

        this.logger.log(`Resolved question source strategy: ${resolvedOptions.resolvedStrategy}, pool enabled: ${resolvedOptions.poolEnabled}`);

        // Validate that we have a valid strategy
        if (!resolvedOptions.resolvedStrategy) {
          this.logger.warn('No valid strategy resolved, defaulting to HYBRID');
          resolvedOptions.resolvedStrategy = SelectionStrategy.HYBRID;
        }

        // Process hybrid question sourcing
        promptResult = await this.processHybridQuestionSourcing({
          worksheetId,
          resolvedOptions,
          documentResults,
          userRequest: {
            grade,
            topic,
            subject, // Add subject information
            parentSubject, // Add parent subject information
            // Pass the complete subject data structure if available
            ...(worksheetDetails.subjectData && { subjectData: worksheetDetails.subjectData }),
            difficulty,
            totalQuestions: totalQuestions, // This is now from job.data.questionCount
            isCustomQuestionCount: isCustomQuestionCount, // Pass the flag
            exerciseType: finalExerciseTypes,
            // Only include distribution if it was specified
            ...(hasDistribution && { exerciseTypeDistribution }),
            includeImages: true,
            // Add schoolId for narrative structure retrieval
            ...(schoolId && { schoolId }),
          },
        });

        // Fallback mechanism: if hybrid sourcing failed or returned no questions, try AI-only as last resort
        if (!promptResult || !promptResult.result || !Array.isArray(promptResult.result) || promptResult.result.length === 0) {
          this.logger.warn('Hybrid sourcing failed or returned no questions. Attempting AI-only fallback.');
          this.socketGateway.emitWorksheetProgress(worksheetId, 0, totalQuestions, 'Fallback: generating with AI...');

          try {
            promptResult = await this.promptService.generatePrompt({
              relevantContent: [documentResults],
              userRequest: {
                grade,
                topic,
                subject,
                parentSubject,
                ...(worksheetDetails.subjectData && { subjectData: worksheetDetails.subjectData }),
                difficulty,
                totalQuestions: totalQuestions,
                isCustomQuestionCount: isCustomQuestionCount,
                exerciseType: finalExerciseTypes,
                ...(hasDistribution && { exerciseTypeDistribution }),
                includeImages: true,
                ...(schoolId && { schoolId }),
              },
              worksheetId,
            });

            this.logger.log('AI-only fallback completed successfully');
          } catch (fallbackError) {
            this.logger.error(`AI-only fallback also failed: ${fallbackError.message}`, fallbackError.stack);
            promptResult = { error: `Both hybrid sourcing and AI fallback failed: ${fallbackError.message}`, stack: fallbackError.stack };
          }
        }

      } catch (error) {
        this.logger.error(`Critical error in question generation: ${error.message}`, error.stack);
        promptResult = { error: error.message, stack: error.stack };
      }

      try {
        // Verify we have exactly the requested number of questions
        if (promptResult && promptResult.result && Array.isArray(promptResult.result)) {
          const actualQuestionCount = promptResult.result.length;

          this.logger.log(`Final verification in consumer: requested ${totalQuestions} questions, got ${actualQuestionCount}`);

          if (actualQuestionCount !== totalQuestions) {
            this.logger.warn(`Question count mismatch: requested ${totalQuestions} but got ${actualQuestionCount}. Enforcing exact count.`);

            if (actualQuestionCount > totalQuestions) {
              // Trim excess questions
              promptResult.result = promptResult.result.slice(0, totalQuestions);
              this.logger.log(`Trimmed excess questions. New count: ${promptResult.result.length}`);
            } else if (actualQuestionCount < totalQuestions && promptResult.result.length > 0) {
              // If we have fewer questions than requested, duplicate some existing ones
              this.logger.log(`Not enough questions (${actualQuestionCount}/${totalQuestions}). Duplicating to meet requirement.`);
              const originalResults = [...promptResult.result];

              while (promptResult.result.length < totalQuestions) {
                // Get a random question from the original set
                const randomIndex = Math.floor(Math.random() * originalResults.length);
                const questionToDuplicate = originalResults[randomIndex];

                // Create a slightly modified copy
                const modifiedQuestion = {
                  ...questionToDuplicate,
                  // Add a note that this is a duplicate (in a way that won't be visible to users)
                  content: questionToDuplicate.content + ' ' // Add a space to make it slightly different
                };

                promptResult.result.push(modifiedQuestion);
              }

              this.logger.log(`Added duplicate questions. New count: ${promptResult.result.length}`);
            }
          }
        } else {
          this.logger.error(`Invalid promptResult structure: ${JSON.stringify(promptResult).substring(0, 200)}...`);
        }

        // Final verification
        const finalCount = promptResult && promptResult.result ? promptResult.result.length : 0;
        this.logger.log(`Final question count before saving: ${finalCount}/${totalQuestions}`);

        // Update the worksheet prompt result with the final result
        await this.worksheetPromptResultModel.findOneAndUpdate(
            { worksheetId },
            {
              promptResult,
              currentQuestionCount: totalQuestions, // Always use the expected count
              totalQuestionCount: totalQuestions,
            }
        );
      } catch (saveError) {
        this.logger.error(`Failed to save prompt result: ${saveError.message}`, saveError.stack);
      }

      const status =
          promptResult && promptResult.error
              ? WorksheetGeneratingStatus.ERROR
              : WorksheetGeneratingStatus.GENERATED;

      // Sync PostgreSQL worksheet data before setting status to GENERATED
      if (status === WorksheetGeneratingStatus.GENERATED && promptResult && promptResult.result) {
        try {
          await this.syncWorksheetQuestionDataToPostgreSQL(worksheetId, promptResult.result);
          this.logger.log(`Successfully synced question data to PostgreSQL for worksheet ${worksheetId}`);
        } catch (syncError) {
          this.logger.error(`Failed to sync question data to PostgreSQL for worksheet ${worksheetId}: ${syncError.message}`, syncError.stack);
          // Don't fail the entire process, but log the error
        }
      }

      await this.worksheetService.updateStatus(worksheetId, status);

      this.socketGateway.emitWorksheetGenerated(worksheetId, {
        status,
        promptResult,
        documentResult: documentResults,
        worksheet: {
          id: worksheetDetails.id,
          title: worksheetDetails.title,
          description: worksheetDetails.description,
          generatingStatus: status,
        },
      });

      // Process batch image generation after questions are generated
      if (status === WorksheetGeneratingStatus.GENERATED) {
        try {
          // Start batch image processing in the background
          this.batchImageService.processBatchImages(worksheetId, topic)
              .then(success => {
                if (success) {
                  this.logger.log(`Batch image processing completed successfully for worksheet ${worksheetId}`);
                } else {
                  this.logger.error(`Batch image processing failed for worksheet ${worksheetId}`);
                }
              })
              .catch(error => {
                this.logger.error(`Error in batch image processing: ${error.message}`, error.stack);
              });
        } catch (imageError) {
          this.logger.error(`Failed to start batch image processing: ${imageError.message}`, imageError.stack);
        }
      }

      return { success: true };
    } catch (error) {
      await this.worksheetService.updateStatus(
          worksheetId,
          WorksheetGeneratingStatus.ERROR,
      );
      throw error;
    }
  }

  /**
   * Emit monitoring event for worksheet generation analytics
   */
  private async emitWorksheetGenerationEvent(
    worksheetId: string,
    strategy: SelectionStrategy,
    totalRequested: number,
    questionsFromPool: number,
    questionsFromAI: number,
    finalQuestionCount: number,
    validatedQuestions: any[],
    userRequest: any
  ): Promise<void> {
    try {
      // Calculate validation metrics
      const totalValidated = validatedQuestions.length;
      const validationSuccessRate = totalValidated > 0 ? finalQuestionCount / totalValidated : 0;

      // Calculate distribution adherence (simplified)
      const difficultyDistribution: Record<string, number> = {};
      const typeDistribution: Record<string, number> = {};

      validatedQuestions.forEach(question => {
        const difficulty = question.difficultyLevel || 'unknown';
        const type = question.type || 'unknown';

        difficultyDistribution[difficulty] = (difficultyDistribution[difficulty] || 0) + 1;
        typeDistribution[type] = (typeDistribution[type] || 0) + 1;
      });

      const event: WorksheetGenerationEvent = {
        eventId: uuidv4(),
        type: 'worksheet_generation',
        timestamp: new Date(),
        worksheetId,
        generationParams: {
          selectionStrategy: strategy,
          totalQuestionsRequested: totalRequested,
          questionTypes: userRequest.exerciseTypes || [],
          subjects: [userRequest.subject].filter(Boolean),
          gradeLevel: userRequest.grade || '',
        },
        result: {
          totalQuestionsGenerated: finalQuestionCount,
          questionsFromPool,
          questionsFromAI,
          executionTimeMs: 0, // This would need to be tracked from the start of the process
          success: finalQuestionCount > 0,
          validationResults: {
            totalValidated,
            validationSuccessRate,
          },
          distributionAdherence: {
            difficultyDistribution,
            typeDistribution,
            targetAchieved: finalQuestionCount >= totalRequested,
          },
        },
      };

      await this.poolMonitoringService.emitEvent(event);
    } catch (error) {
      // Don't throw error to avoid disrupting main flow
      this.logger.error(`Error emitting worksheet generation event: ${error.message}`, error.stack);
    }
  }

  /**
   * Sync worksheet question data from MongoDB to PostgreSQL
   * @param worksheetId - The worksheet ID to update
   * @param mongoQuestions - Array of questions from MongoDB promptResult
   */
  private async syncWorksheetQuestionDataToPostgreSQL(
    worksheetId: string,
    mongoQuestions: any[]
  ): Promise<void> {
    try {
      this.logger.log(`Starting PostgreSQL sync for worksheet ${worksheetId} with ${mongoQuestions.length} questions`);

      // Since questions are already saved in WorksheetPromptResult, we just need to update PostgreSQL
      // We'll use the MongoDB document _id as a reference, but since WorksheetPromptResult doesn't have individual _ids,
      // we'll generate question references based on the array index
      const questionIds: string[] = [];

      // Generate question IDs based on worksheet ID and question index
      for (let i = 0; i < mongoQuestions.length; i++) {
        const questionRef = `${worksheetId}_q${i + 1}`;
        questionIds.push(questionRef);
      }

      // Create question metadata
      const questionMetadata = {
        lastQuestionUpdate: new Date(),
        questionVersion: 1,
        hasUnsavedChanges: false,
        collaborators: [],
        lockStatus: {
          isLocked: false
        }
      };

      // Update PostgreSQL worksheet with question data
      await this.worksheetService.updateQuestionData(
        worksheetId,
        questionIds,
        mongoQuestions.length,
        questionMetadata
      );

      this.logger.log(`Successfully synced ${mongoQuestions.length} questions to PostgreSQL for worksheet ${worksheetId}`);

    } catch (error) {
      this.logger.error(`Error syncing worksheet question data to PostgreSQL: ${error.message}`, error.stack);
      throw error;
    }
  }
}
